export default {
	color: {
		red: 0xff0000,
		green: 0x00ff00,
		blue: 0x0000ff,
		yellow: 0xffff00,
		main: 0x2f3136,
	},
	emoji: {
		// You can add custom emoji with ID format (e.g., <:emojiName:123456789012345678>)
		pause: '⏸️',
		resume: '▶️',
		stop: '⏹️',
		skip: '⏭️',
		previous: '⏮️',
		forward: '⏩',
		rewind: '⏪',
		voldown: '🔉',
		volup: '🔊',
		shuffle: '🔀',
		loop: {
			none: '🔁',
			track: '🔂',
		},
		page: {
			last: '⏩',
			first: '⏪',
			back: '⬅️',
			next: '➡️',
			cancel: '⏹️',
		},
	},
	icons: {
		youtube: 'https://i.imgur.com/xzVHhFY.png',
		spotify: 'https://i.imgur.com/qvdqtsc.png',
		soundcloud: 'https://i.imgur.com/MVnJ7mj.png',
		applemusic: 'https://i.imgur.com/Wi0oyYm.png',
		deezer: 'https://i.imgur.com/xyZ43FG.png',
		jiosaavn: 'https://i.imgur.com/N9Nt80h.png',
	} as any,
	links: {
		img: 'https://i.imgur.com/ud3EWNh.jpg',
	},
};

/**
 * Project: lavamusic
 * Author: Appu
 * Main Contributor: LucasB25
 * Company: Coders
 * Copyright (c) 2024. All rights reserved.
 * This code is the property of Coder and may not be reproduced or
 * modified without permission. For more information, contact us at
 * https://discord.gg/YQsGbTwPBx
 */
