name: Build and publish a Docker image to ghcr.io
on: push

jobs:
    docker_publish:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout
              uses: actions/checkout@v4
            - name: Set up QEMU
              uses: docker/setup-qemu-action@v3
            - name: Set up Docker Buildx
              id: buildx
              uses: docker/setup-buildx-action@v3
            - name: Available platforms
              run: echo ${{ steps.buildx.outputs.platforms }}
            - name: Docker meta
              id: meta
              uses: docker/metadata-action@v5
              with:
                  images: ghcr.io/appujet/lavamusic
            - name: Login to GitHub Container Registry
              uses: docker/login-action@v3
              with:
                  registry: ghcr.io
                  username: ${{ github.repository_owner }}
                  password: ${{ secrets.GITHUB_TOKEN }}
            - name: Build and push
              uses: docker/build-push-action@v6
              with:
                  context: .
                  push: ${{ github.event_name != 'pull_request' }}
                  tags: ${{ steps.meta.outputs.tags }}
                  labels: ${{ steps.meta.outputs.labels }}
                  platforms: |
                      linux/amd64
                      linux/arm64