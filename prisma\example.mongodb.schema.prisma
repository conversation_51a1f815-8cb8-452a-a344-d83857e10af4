// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model Bot {
  botId         String @id @map("_id")
  totalPlaySong Int
}

model Guild {
  guildId  String  @id @map("_id")
  prefix   String
  language String? @default("EnglishUS")
  stay     Stay?
  dj       Dj?
  roles    Role[]
  setup    Setup?
}

model Stay {
  guildId String @id @map("_id")
  textId  String
  voiceId String
  Guild   Guild  @relation(fields: [guildId], references: [guildId])
}

model Dj {
  guildId String  @id @map("_id")
  mode    Boolean
  Guild   Guild   @relation(fields: [guildId], references: [guildId])
}

model Role {
  guildId String @id @map("_id")
  roleId  String
  Guild   Guild  @relation(fields: [guildId], references: [guildId])

  @@unique([guildId, roleId])
}

model Playlist {
  id     String  @id @default(uuid()) @map("_id")
  userId String
  name   String
  tracks String? // Store the array of encoded tracks as a JSON string

  @@unique([userId, name])
}

model Setup {
  guildId   String @id @map("_id")
  textId    String
  messageId String
  Guild     Guild  @relation(fields: [guildId], references: [guildId])
}
