{
  "compilerOptions": {
    "target": "ESNext",
    "lib": ["ESNext", "WebWorker"],
    "module": "CommonJS",
    "moduleResolution": "node",
    "declaration": true,
    "sourceMap": false,
    "removeComments": true,
    "importHelpers": true,
    "strict": true,
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "preserveConstEnums": true,
    /* Type Checking */
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "skipLibCheck": true,
    "noErrorTruncation": true,
    "outDir": "./dist",
    "stripInternal": true
  },
  "exclude": ["dist/", "node_modules/"],
  "include": ["src/**/*"]
}
