{
  "cmd": {
    "247": {
      "description": "Stel de bot in om in het spraakkanaal te blijven",
      "errors": {
        "not_in_voice": "Je moet in een spraakkanaal zitten om dit commando te kunnen gebruiken.",
        "generic": "Er is een probleem opgetreden tijdens het uivoeren van dit commando."
      },
      "messages": {
        "disabled": "`✅` | 24/7 modus is `UITGESCHAKELD`.",
        "enabled": "`✅` | 24/7 modus is `INGESCHAKELD`."
      }
    },
    "ping": {
      "description": "Laat ping van de bot zien.",
      "content": "Pingen...",
      "bot_latency": "Bot Latentie",
      "api_latency": "API Latentie",
      "requested_by": "Aangevraagd door: {author}"
    },
    "lavalink": {
      "description": "Laat de huidige Lavalink statestieken zien.",
      "title": "Lavalink statestieken",
      "content": "Speler: {players}\nSpelende spelers: {playingPlayers}\nUptime: {uptime}\nCores: {cores} Core(s)\nWerkgeheugen Gebruik: {used} / {reservable}\nSysteem Belasting: {systemLoad}%\nLavalink Belasting: {lavalinkLoad}%",
      "page_info": "Pagina {index} van {total}"
    },
    "invite": {
      "description": "Verkrijg de bot invite link.",
      "content": "Je kunt me uitnodigen door op de onderstaande knop te klikken. Bugs of andere problemen? Word lid van de ondersteuningsserver!"
    },
    "help": {
      "description": "Laat het help menu zien.",
      "options": {
        "command": "Het commando waarvan je informatie wilt hebben."
      },
      "content": "Hoi! Ik ben {bot}, een muziek bot gemaakt met [Lavamusic](https://github.com/appujet/lavamusic) en Discord.js. Je kunt `{prefix}help <commando>` gebruiken om meer informatie te krijgen over een commando.",
      "title": "Help Menu",
      "not_found": "Dit `{cmdName}` commando bestaat niet.",
      "help_cmd": "**Beschrijving:** {description}\n**Gebruik:** {usage}\n**Voorbeelden:** {examples}\n**Aliassen:** {aliases}\n**Categorie:** {category}\n**Afkoeltijd:** {cooldown} seconden\n**Rechten:** {premUser}\n**Bot Rechten:** {premBot}\n**Alleen voor Ontwikkelaar:** {dev}\n**Slash Commando:** {slash}\n**Argumenten:** {args}\n**Speler:** {player}\n**DJ:** {dj}\n**DJ Rechten:** {djPerm}\n**Stem:** {voice}",
      "footer": "Gebruik {prefix}help <commando> voor meer informatie over een commando"
    },
    "botinfo": {
      "description": "Informatie over de bot",
      "content": "Bot informatie:\n- **Besturingssysteem**: {osInfo}\n- **Uptime**: {osUptime}\n- **Hostnaam**: {osHostname}\n- **CPU Architectuur**: {cpuInfo}\n- **CPU Gebruik**: {cpuUsed}%\n- **Werkgeheugen Gebruik**: {memUsed}MB / {memTotal}GB\n- **Node Versie**: {nodeVersion}\n- **Discord.js Versie**: {discordJsVersion}\n- **Verbonden met** {guilds} servers, {channels} kanalen, en {users} gebruikers\n- **Totaal aantal commando's**: {commands}"
    },
    "about": {
      "description": "Laat informatie over de bot zien",
      "fields": {
        "creator": "Auteur",
        "repository": "Repository",
        "support": "Ondersteuning",
        "description": "Hij wilde heel erg graag zijn eerste open-source project maken voor meer programmeerervaring. In dit project werd hij uitgedaagd om een project te maken met minder bugs. Hopelijk heb je plezier van het gebruik van LavaMusic!"
      }
    },
    "dj": {
      "description": "Beheer de DJ-modus en bijbehorende rollen",
      "errors": {
        "provide_role": "Geef alsjeblieft een rol op.",
        "no_roles": "De DJ-rol is leeg.",
        "invalid_subcommand": "Geef alsjeblieft een geldig subcommando op."
      },
      "messages": {
        "role_exists": "De DJ-rol <@&{roleId}> is al toegevoegd.",
        "role_added": "De DJ-rol <@&{roleId}> is toegevoegd.",
        "role_not_found": "De DJ-rol <@&{roleId}> is niet toegevoegd.",
        "role_removed": "De DJ-rol <@&{roleId}> is verwijderd.",
        "all_roles_cleared": "Alle DJ-rollen zijn verwijderd.",
        "toggle": "De DJ-modus is ingesteld op {status}."
      },
      "options": {
        "add": "De DJ-rol die je wilt toevoegen",
        "remove": "De DJ-rol die je wilt verwijderen",
        "clear": "Verwijdert alle DJ-rollen",
        "toggle": "Schakelt de DJ-rol in/uit",
        "role": "De DJ-rol"
      },
      "subcommands": "Subcommando's"
    },
    "language": {
      "description": "Stel de taal in voor de bot",
      "invalid_language": "Geef alsjeblieft een geldige taal op. Voorbeeld: `EnglishUS` voor Engels (Verenigde Staten)\n\nJe kunt de lijst met ondersteunde talen [hier](https://discord.com/developers/docs/reference#locales) vinden\n\n**Beschikbare talen:**\n{languages}",
      "already_set": "De taal is al ingesteld op `{language}`",
      "not_set": "De taal is niet ingesteld.",
      "set": "`✅` | De taal is ingesteld op `{language}`",
      "reset": "`✅` | De taal is teruggezet naar de standaardtaal.",
      "options": {
        "set": "Stel de taal in voor de bot",
        "language": "De taal die je wilt instellen",
        "reset": "Zet de taal terug naar de standaardtaal"
      }
    },
    "prefix": {
      "description": "Toont of stelt het voorvoegsel van de bot in",
      "errors": {
        "prefix_too_long": "Het voorvoegsel mag niet langer zijn dan 3 tekens."
      },
      "messages": {
        "current_prefix": "Het voorvoegsel voor deze server is `{prefix}`",
        "prefix_set": "Het voorvoegsel voor deze server is nu `{prefix}`",
        "prefix_reset": "Het voorvoegsel voor deze server is nu `{prefix}`"
      },
      "options": {
        "set": "Stelt het voorvoegsel in",
        "prefix": "Het voorvoegsel dat je wilt instellen",
        "reset": "Zet het voorvoegsel terug naar de standaardwaarde"
      }
    },
    "setup": {
      "description": "Stelt de bot in",
      "errors": {
        "channel_exists": "Het verzoeknummerskanaal bestaat al.",
        "channel_not_exists": "Het verzoeknummerskanaal bestaat niet.",
        "channel_delete_fail": "Het instellingskanaal is verwijderd uit de database. Verwijder het kanaal zelf alsjeblieft."
      },
      "messages": {
        "channel_created": "Het verzoeknummerskanaal is aangemaakt in <#{channelId}>.",
        "channel_deleted": "Het verzoeknummerskanaal is verwijderd.",
        "channel_info": "Het verzoeknummerskanaal is <#{channelId}>."
      },
      "options": {
        "create": "Maakt het verzoeknummerskanaal aan",
        "delete": "Verwijdert het verzoeknummerskanaal",
        "info": "Toont het verzoeknummerskanaal"
      }
    },
    "8d": {
      "description": "Zet 8d-filter aan/uit",
      "messages": {
        "filter_enabled": "`✅` | 8D-filter is `INGESCHAKELD`.",
        "filter_disabled": "`✅` | 8D-filter is `UITGESCHAKELD`."
      }
    },
    "bassboost": {
      "description": "Zet bassboost-filter aan/uit",
      "options": {
        "level": "Het bassboost-niveau dat je wilt instellen"
      },
      "messages": {
        "high": "`✅` | Hoog bassboost-filter is `INGESCHAKELD`.",
        "low": "`✅` | Laag bassboost-filter is `INGESCHAKELD`.",
        "medium": "`✅` | Gemiddeld bassboost-filter is `INGESCHAKELD`.",
        "off": "`✅` | Bassboost-filter is `UITGESCHAKELD`."
      }
    },
    "distortion": {
      "description": "Schakel het vervormingsfilter in/uit",
      "messages": {
        "filter_enabled": "`✅` | Vervormingsfilter is `INGESCHAKELD`.",
        "filter_disabled": "`✅` | Vervormingsfilter is `UITGESCHAKELD`."
      }
    },
    "karaoke": {
      "description": "Schakel het karaoke-filter in/uit",
      "messages": {
        "filter_enabled": "`✅` | Karaoke-filter is `INGESCHAKELD`.",
        "filter_disabled": "`✅` | Karaoke-filter is `UITGESCHAKELD`."
      }
    },
    "lowpass": {
      "description": "Schakel het laagdoorlaatfilter in/uit",
      "messages": {
        "filter_enabled": "`✅` | Laagdoorlaatfilter is `INGESCHAKELD`.",
        "filter_disabled": "`✅` | Laagdoorlaatfilter is `UITGESCHAKELD`."
      }
    },
    "nightcore": {
      "description": "Schakel het nightcore-filter in/uit",
      "messages": {
        "filter_enabled": "`✅` | Nightcore-filter is `INGESCHAKELD`.",
        "filter_disabled": "`✅` | Nightcore-filter is `UITGESCHAKELD`."
      }
    },
    "pitch": {
      "description": "Schakel het toonhoogtefilter in/uit",
      "options": {
        "pitch": "Het nummer waarmee je de toonhoogte wilt instellen (tussen 0,5 en 5)"
      },
      "errors": {
        "invalid_number": "Geef een geldig nummer op tussen 0,5 en 5."
      },
      "messages": {
        "pitch_set": "`✅` | Toonhoogte is ingesteld op **{pitch}**."
      }
    },
    "rate": {
      "description": "Verander de afspeelsnelheid van het nummer",
      "options": {
        "rate": "Het nummer waarmee je de afspeelsnelheid wilt instellen (tussen 0,5 en 5)"
      },
      "errors": {
        "invalid_number": "Geef een geldig nummer op tussen 0,5 en 5."
      },
      "messages": {
        "rate_set": "`✅` | Afspeelsnelheid is ingesteld op **{rate}**."
      }
    },
    "reset": {
      "description": "Reset de actieve filters",
      "messages": {
        "filters_reset": "`✅` | Filters zijn gereset."
      }
    },
    "rotation": {
      "description": "Schakel het rotatiefilter in/uit",
      "messages": {
        "enabled": "`✅` | Rotatiefilter is `INGESCHAKELD`.",
        "disabled": "`✅` | Rotatiefilter is `UITGESCHAKELD`."
      }
    },
    "speed": {
      "description": "Verander de snelheid van het nummer",
      "options": {
        "speed": "De snelheid die je wilt instellen"
      },
      "messages": {
        "invalid_number": "Geef een geldig nummer op tussen 0,5 en 5.",
        "set_speed": "`✅` | Snelheid is ingesteld op **{speed}**."
      }
    },
    "tremolo": {
      "description": "Schakel het tremolo-filter in/uit",
      "messages": {
        "enabled": "`✅` | Tremolo-filter is `INGESCHAKELD`.",
        "disabled": "`✅` | Tremolo-filter is `UITGESCHAKELD`."
      }
    },
    "vibrato": {
      "description": "Schakel het vibrato-filter in/uit",
      "messages": {
        "enabled": "`✅` | Vibrato-filter is `INGESCHAKELD`.",
        "disabled": "`✅` | Vibrato-filter is `UITGESCHAKELD`."
      }
    },
    "autoplay": {
      "description": "Schakelt automatisch afspelen in/uit",
      "messages": {
        "enabled": "`✅` | Automatisch afspelen is `INGESCHAKELD`.",
        "disabled": "`✅` | Automatisch afspelen is `UITGESCHAKELD`."
      }
    },
    "clearqueue": {
      "description": "Wist de wachtrij",
      "messages": {
        "cleared": "De wachtrij is gewist."
      }
    },
    "grab": {
      "description": "Pakt het huidige nummer en stuurt het naar je DM",
      "loading": "Laden...",
      "content": "**Duur:** {length}\n**Aangevraagd door:** <@{requester}>\n**Link:** [Klik hier]({uri})",
      "check_dm": "Controleer je DM.",
      "dm_failed": "Ik kon je geen DM sturen. Zorg ervoor dat directe berichten zijn ingeschakeld."
    },
    "join": {
      "description": "Treedt toe tot het spraakkanaal",
      "already_connected": "Ik ben al verbonden met <#{channelId}>.",
      "no_voice_channel": "Je moet in een spraakkanaal zitten om dit commando te gebruiken.",
      "joined": "Succesvol toegetreden tot <#{channelId}>."
    },
    "leave": {
      "description": "Verlaat het spraakkanaal",
      "left": "Succesvol <#{channelId}> verlaten.",
      "not_in_channel": "Ik ben niet in een spraakkanaal."
    },
    "loop": {
      "description": "Herhaal het huidige nummer of de wachtrij",
      "looping_song": "**Nummer wordt herhaald.**",
      "looping_queue": "**Wachtrij wordt herhaald.**",
      "looping_off": "**Herhalen is nu uitgeschakeld.**"
    },
    "lyrics": {
      "description": "Haalt de songtekst op van het huidige nummer",
      "lyrics_track": "### Songtekst voor: [{trackTitle}]({trackUrl})\n**`{lyrics}`**",
      "searching": "`🔍` Zoeken naar songtekst van **{trackTitle}**...",
      "errors": {
        "no_results": "Geen songtekst gevonden voor het huidige nummer.",
        "lyrics_error": "Er is een fout opgetreden bij het ophalen van de songtekst."
      }
    },
    "nowplaying": {
      "description": "Toont het huidige nummer",
      "now_playing": "Nu Spelend",
      "track_info": "[{title}]({uri}) - Aangevraagd door: <@{requester}>\n\n`{bar}`"
    },
    "pause": {
      "description": "Pauzeert het huidige nummer",
      "successfully_paused": "Het nummer is succesvol gepauzeerd."
    },
    "play": {
      "description": "Speelt een nummer af van YouTube, Spotify of http",
      "options": {
        "song": "Het nummer dat je wilt afspelen"
      },
      "loading": "Laden...",
      "errors": {
        "search_error": "Er is een fout opgetreden tijdens het zoeken.",
        "no_results": "Er zijn geen resultaten gevonden.",
        "queue_too_long": "De wachtrij is te lang. De maximale lengte is {maxQueueSize} nummers.",
        "playlist_too_long": "De afspeellijst is te lang. De maximale lengte is {maxPlaylistSize} nummers."
      },
      "added_to_queue": "[{title}]({uri}) is toegevoegd aan de wachtrij.",
      "added_playlist_to_queue": "{length} nummers zijn toegevoegd aan de wachtrij."
    },
    "playnext": {
      "description": "Voegt het nummer toe om als volgende af te spelen in de wachtrij",
      "options": {
        "song": "Het nummer dat je als volgende wilt afspelen"
      },
      "loading": "Laden...",
      "errors": {
        "search_error": "Er is een fout opgetreden tijdens het zoeken.",
        "no_results": "Er zijn geen resultaten gevonden.",
        "queue_too_long": "De wachtrij is te lang. De maximale lengte is {maxQueueSize} nummers.",
        "playlist_too_long": "De afspeellijst is te lang. De maximale lengte is {maxPlaylistSize} nummers."
      },
      "added_to_play_next": "[{title}]({uri}) is toegevoegd om als volgende af te spelen in de wachtrij.",
      "added_playlist_to_play_next": "{length} nummers zijn toegevoegd om als volgende af te spelen in de wachtrij."
    },
    "queue": {
      "description": "Toont de huidige wachtrij",
      "now_playing": "Nu spelend: [{title}]({uri}) - Aangevraagd door: <@{requester}> - Duur: `{duration}`",
      "live": "LIVE",
      "track_info": "{index}. [{title}]({uri}) - Aangevraagd door: <@{requester}> - Duur: `{duration}`",
      "title": "Wachtrij",
      "page_info": "Pagina {index} van {total}"
    },
    "remove": {
      "description": "Verwijdert een nummer uit de wachtrij",
      "options": {
        "song": "Het nummer dat je wilt verwijderen"
      },
      "errors": {
        "no_songs": "Er zijn geen nummers in de wachtrij.",
        "invalid_number": "Geef een geldig nummer op."
      },
      "messages": {
        "removed": "Nummer {songNumber} is verwijderd uit de wachtrij."
      }
    },
    "replay": {
      "description": "Speelt het huidige nummer opnieuw af",
      "errors": {
        "not_seekable": "Dit nummer kan niet opnieuw worden afgespeeld omdat het niet doorzoekbaar is."
      },
      "messages": {
        "replaying": "Het huidige nummer wordt opnieuw afgespeeld."
      }
    },
    "resume": {
      "description": "Hervat het huidige nummer",
      "errors": {
        "not_paused": "De speler is niet gepauzeerd."
      },
      "messages": {
        "resumed": "De speler is hervat."
      }
    },
    "search": {
      "description": "Zoekt naar een nummer",
      "options": {
        "song": "Het nummer waarnaar je wilt zoeken"
      },
      "select": "Selecteer het nummer dat je wilt afspelen",
      "errors": {
        "no_results": "Geen resultaten gevonden.",
        "search_error": "Er is een fout opgetreden tijdens het zoeken."
      },
      "messages": {
        "added_to_queue": "[{title}]({uri}) is toegevoegd aan de wachtrij."
      }
    },
    "seek": {
      "description": "Springt naar een bepaald tijdstip in het nummer",
      "options": {
        "duration": "De tijd waarnaar je wilt springen"
      },
      "errors": {
        "invalid_format": "Ongeldig tijdsformaat. Voorbeelden: seek 1m, seek 1h 30m",
        "not_seekable": "Dit nummer is niet doorzoekbaar.",
        "beyond_duration": "Kan niet verder springen dan de duur van het nummer van {length}."
      },
      "messages": {
        "seeked_to": "Gesprongen naar {duration}"
      }
    },
    "shuffle": {
      "description": "Schudt de wachtrij door elkaar",
      "messages": {
        "shuffled": "De wachtrij is door elkaar geschud."
      }
    },
    "skip": {
      "description": "Slaat het huidige nummer over",
      "messages": {
        "skipped": "[{title}]({uri}) is overgeslagen."
      }
    },
    "skipto": {
      "description": "Slaat over naar een specifiek nummer in de wachtrij",
      "options": {
        "number": "Het nummer waarnaar je wilt overspringen"
      },
      "errors": {
        "invalid_number": "Geef een geldig nummer op."
      },
      "messages": {
        "skipped_to": "Overgeslagen naar nummer {number}."
      }
    },
    "stop": {
      "description": "Stopt de muziek en wist de wachtrij",
      "messages": {
        "stopped": "De muziek is gestopt en de wachtrij is gewist."
      }
    },
    "volume": {
      "description": "Stelt het volume van de speler in",
      "options": {
        "number": "Het volume dat je wilt instellen"
      },
      "messages": {
        "invalid_number": "Geef een geldig nummer op.",
        "too_low": "Het volume kan niet lager zijn dan 0.",
        "too_high": "Het volume kan niet hoger zijn dan 200. Wil je je gehoor of luidsprekers beschadigen? Hmm, ik denk niet dat dat zo'n goed idee is.",
        "set": "Het volume is ingesteld op {volume}"
      }
    },
    "addsong": {
      "description": "Voegt een nummer toe aan de afspeellijst",
      "options": {
        "playlist": "De afspeellijst waaraan je wilt toevoegen",
        "song": "Het nummer dat je wilt toevoegen"
      },
      "messages": {
        "no_playlist": "Geef een afspeellijst op",
        "no_song": "Geef een nummer op",
        "playlist_not_found": "Die afspeellijst bestaat niet",
        "no_songs_found": "Geen nummers gevonden",
        "added": "{count} nummer(s) toegevoegd aan {playlist}"
      }
    },
    "create": {
      "description": "Maakt een afspeellijst aan",
      "options": {
        "name": "De naam van de afspeellijst"
      },
      "messages": {
        "name_too_long": "Afspeellijstnamen mogen maximaal 50 tekens lang zijn.",
        "playlist_exists": "Er bestaat al een afspeellijst met die naam. Gebruik een andere naam.",
        "playlist_created": "Afspeellijst **{name}** is aangemaakt."
      }
    },
    "delete": {
      "description": "Verwijdert een afspeellijst",
      "options": {
        "playlist": "De afspeellijst die je wilt verwijderen"
      },
      "messages": {
        "playlist_not_found": "Die afspeellijst bestaat niet.",
        "playlist_deleted": "Afspeellijst **{playlistName}** is verwijderd."
      }
    },
    "list": {
      "description": "Haalt alle afspeellijsten op voor de gebruiker",
      "options": {
        "user": "De gebruiker van wie je de afspeellijsten wilt ophalen"
      },
      "messages": {
        "no_playlists": "Deze gebruiker heeft geen afspeellijsten.",
        "your": "Jouw",
        "playlists_title": "Afspeellijsten van {username}",
        "error": "Er is een fout opgetreden bij het ophalen van de afspeellijsten."
      }
    },
    "load": {
      "description": "Laadt een afspeellijst",
      "options": {
        "playlist": "De afspeellijst die je wilt laden"
      },
      "messages": {
        "playlist_not_exist": "Die afspeellijst bestaat niet.",
        "playlist_empty": "Die afspeellijst is leeg.",
        "playlist_loaded": "`{name}` met `{count}` nummers is geladen."
      }
    },
    "removesong": {
      "description": "Verwijdert een nummer uit de afspeellijst",
      "options": {
        "playlist": "De afspeellijst waaruit je wilt verwijderen",
        "song": "Het nummer dat je wilt verwijderen"
      },
      "messages": {
        "provide_playlist": "Geef een afspeellijst op.",
        "provide_song": "Geef een nummer op.",
        "playlist_not_exist": "Die afspeellijst bestaat niet.",
        "song_not_found": "Geen overeenkomend nummer gevonden.",
        "song_removed": "{song} is verwijderd uit {playlist}.",
        "error_occurred": "Er is een fout opgetreden bij het verwijderen van het nummer."
      }
    },
    "steal": {
      "description": "Steelt een afspeellijst van een andere gebruiker en voegt deze toe aan jouw afspeellijsten",
      "options": {
        "playlist": "De afspeellijst die je wilt stelen",
        "user": "De gebruiker van wie je de afspeellijst wilt stelen"
      },
      "messages": {
        "provide_playlist": "Geef een afspeellijstnaam op.",
        "provide_user": "Vermeld een gebruiker.",
        "playlist_not_exist": "Die afspeellijst bestaat niet voor de vermelde gebruiker.",
        "playlist_stolen": "De afspeellijst `{playlist}` is succesvol gestolen van {user}.",
        "error_occurred": "Er is een fout opgetreden bij het stelen van de afspeellijst."
      }
    }
  },
  "buttons": {
    "invite": "Uitnodigen",
    "support": "Ondersteuningsserver",
    "previous": "Vorige",
    "resume": "Hervatten",
    "stop": "Stoppen",
    "skip": "Overslaan",
    "loop": "Herhalen",
    "errors": {
      "not_author": "Je kunt deze knop niet gebruiken."
    }
  },
  "player": {
    "errors": {
      "no_player": "Er is geen actieve speler in deze server.",
      "no_channel": "Je moet in een spraakkanaal zitten om dit commando te gebruiken.",
      "queue_empty": "De wachtrij is leeg.",
      "no_previous": "Er zijn geen vorige nummers in de wachtrij.",
      "no_song": "Er zijn geen nummers in de wachtrij.",
      "already_paused": "Het nummer is al gepauzeerd."
    },
    "trackStart": {
      "now_playing": "Nu Spelend",
      "requested_by": "Aangevraagd door {user}",
      "duration": "Duur",
      "author": "Auteur",
      "not_connected_to_voice_channel": "Je bent niet verbonden met <#{channel}> om deze knoppen te gebruiken.",
      "need_dj_role": "Je moet de DJ-rol hebben om dit commando te gebruiken.",
      "previous_by": "Vorige door {user}",
      "no_previous_song": "Er is geen vorig nummer.",
      "paused_by": "Gepauzeerd door {user}",
      "resumed_by": "Hervat door {user}",
      "skipped_by": "Overgeslagen door {user}",
      "no_more_songs_in_queue": "Er zijn geen nummers meer in de wachtrij.",
      "looping_by": "Herhalen ingeschakeld door {user}",
      "looping_queue_by": "Wachtrij herhalen ingeschakeld door {user}",
      "looping_off_by": "Herhalen uitgeschakeld door {user}"
    },
    "setupStart": {
      "now_playing": "Nu Spelend",
      "description": "[{title}]({uri}) door {author} • `[{length}]` - Aangevraagd door <@{requester}>",
      "error_searching": "Er is een fout opgetreden tijdens het zoeken.",
      "no_results": "Er zijn geen resultaten gevonden.",
      "nothing_playing": "Er wordt momenteel niets afgespeeld",
      "queue_too_long": "De wachtrij is te lang. De maximale lengte is {maxQueueSize} nummers.",
      "playlist_too_long": "De afspeellijst is te lang. De maximale lengte is {maxPlaylistSize} nummers.",
      "added_to_queue": "[{title}]({uri}) is toegevoegd aan de wachtrij.",
      "added_playlist_to_queue": "[{length}] nummers van de afspeellijst zijn toegevoegd aan de wachtrij."
    },
  },
  "event": {
    "interaction": {
      "setup_channel": "Je kunt dit commando niet gebruiken in het instellingskanaal.",
      "no_send_message": "Ik heb geen **`SendMessage`**, **`ViewChannel`**, **`EmbedLinks`** of **`ReadMessageHistory`** rechten.",
      "no_permission": "Ik heb geen {permissions} rechten.",
      "no_user_permission": "Je hebt niet genoeg rechten om dit commando te gebruiken.",
      "no_voice_channel": "Je moet verbonden zijn met een spraakkanaal om dit `{command}` commando te gebruiken.",
      "no_connect_permission": "Ik heb geen `CONNECT` rechten om dit `{command}` commando uit te voeren.",
      "no_speak_permission": "Ik heb geen `SPEAK` rechten om dit `{command}` commando uit te voeren.",
      "no_request_to_speak": "Ik heb geen `REQUEST TO SPEAK` rechten om dit `{command}` commando uit te voeren.",
      "different_voice_channel": "Je bent niet verbonden met {channel} om dit `{command}` commando te gebruiken.",
      "no_music_playing": "Er wordt momenteel niets afgespeeld.",
      "no_dj_role": "De DJ-rol is niet ingesteld.",
      "no_dj_permission": "Je moet de DJ-rol hebben om dit commando te gebruiken.",
      "cooldown": "Wacht nog {time} seconde(n) voordat je het `{command}` commando opnieuw gebruikt.",
      "error": "Er is een fout opgetreden: `{error}`",
      "vote_button": "Stem op mij!",
      "vote_message": "Wacht! Je moet stemmen op top.gg om dit commando te gebruiken. Bedankt."
    },
    "message": {
      "prefix_mention": "Hé, mijn voorvoegsel voor deze server is `{prefix}`. Wil je meer informatie? Gebruik dan `{prefix}help`\nBlijf veilig, blijf geweldig!",
      "no_send_message": "Ik heb geen **`SendMessage`**, **`ViewChannel`**, **`EmbedLinks`** of **`ReadMessageHistory`** rechten.",
      "no_permission": "Ik heb geen {permissions} rechten.",
      "no_user_permission": "Je hebt niet genoeg rechten om dit commando te gebruiken.",
      "no_voice_channel": "Je moet verbonden zijn met een spraakkanaal om dit `{command}` commando te gebruiken.",
      "no_connect_permission": "Ik heb geen `CONNECT` rechten om dit `{command}` commando uit te voeren.",
      "no_speak_permission": "Ik heb geen `SPEAK` rechten om dit `{command}` commando uit te voeren.",
      "no_request_to_speak": "Ik heb geen `REQUEST TO SPEAK` rechten om dit `{command}` commando uit te voeren.",
      "different_voice_channel": "Je bent niet verbonden met {channel} om dit `{command}` commando te gebruiken.",
      "no_music_playing": "Er wordt momenteel niets afgespeeld.",
      "no_dj_role": "De DJ-rol is niet ingesteld.",
      "no_dj_permission": "Je moet de DJ-rol hebben om dit commando te gebruiken.",
      "missing_arguments": "Ontbrekende argumenten",
      "missing_arguments_description": "Geef de vereiste argumenten op voor het `{command}` commando.\n\nVoorbeelden:\n{examples}",
      "syntax_footer": "Syntax: [] = optioneel, <> = verplicht",
      "cooldown": "Wacht nog {time} seconde(n) voordat je het `{command}` commando opnieuw gebruikt.",
      "no_mention_everyone": "Je kunt dit commando niet gebruiken met everyone of here. Gebruik alsjeblieft het slash-commando.",
      "error": "Er is een fout opgetreden: `{error}`",
      "no_voice_channel_queue": "Je bent niet verbonden met een spraakkanaal om nummers in de wachtrij te zetten.",
      "no_permission_connect_speak": "Ik heb niet genoeg rechten om te verbinden/spreken in <#{channel}>.",
      "different_voice_channel_queue": "Je bent niet verbonden met <#{channel}> om nummers in de wachtrij te zetten.",
      "vote_button": "Stem op mij!",
      "vote_message": "Wacht! Je moet stemmen op top.gg om dit commando te gebruiken. Bedankt."
    },
    "setupButton": {
      "no_voice_channel_button": "Je bent niet verbonden met een spraakkanaal om deze knop te gebruiken.",
      "different_voice_channel_button": "Je bent niet verbonden met {channel} om deze knoppen te gebruiken.",
      "now_playing": "Nu Spelend",
      "live": "LIVE",
      "requested_by": "Aangevraagd door <@{requester}>",
      "no_dj_permission": "Je moet de DJ-rol hebben om deze knop te gebruiken.",
      "volume_set": "Volume ingesteld op {vol}%",
      "volume_footer": "Volume: {vol}%",
      "paused": "Gepauzeerd",
      "resumed": "Hervat",
      "pause_resume": "Muziek {name}.",
      "pause_resume_footer": "{name} door {displayName}",
      "no_music_to_skip": "Er is geen muziek om over te slaan.",
      "skipped": "Muziek overgeslagen.",
      "skipped_footer": "Overgeslagen door {displayName}",
      "stopped": "Muziek gestopt.",
      "stopped_footer": "Gestopt door {displayName}",
      "nothing_playing": "Er wordt momenteel niets afgespeeld",
      "loop_set": "Herhalen ingesteld op {loop}.",
      "loop_footer": "Herhalen ingesteld op {loop} door {displayName}",
      "shuffled": "Wachtrij door elkaar geschud.",
      "no_previous_track": "Er is geen vorig nummer.",
      "playing_previous": "Vorig nummer wordt afgespeeld.",
      "previous_footer": "Vorig nummer afgespeeld door {displayName}",
      "rewinded": "Muziek teruggespoeld.",
      "rewind_footer": "Teruggespoeld door {displayName}",
      "forward_limit": "Je kunt de muziek niet verder vooruitspoelen dan de lengte van het nummer.",
      "forwarded": "Muziek vooruitgespoeld.",
      "forward_footer": "Vooruitgespoeld door {displayName}",
      "button_not_available": "Deze knop is niet beschikbaar.",
      "no_music_playing": "Er wordt geen muziek afgespeeld in deze server."
    }
  },
  "Evaluate code": "Code evalueren",
  "Leave a guild": "Een server verlaten",
  "List all guilds the bot is in": "Toon alle servers waar de bot in zit",
  "Restart the bot": "Herstart de bot"
}
