// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "sqlite"
  url      = "file:./lavamusic.db"
}

model Bot {
  botId         String @unique
  totalPlaySong Int    @default(0)
}

model Guild {
  guildId  String  @id
  prefix   String
  language String? @default("EnglishUS")
  stay     Stay?
  dj       Dj?
  roles    Role[]
  setup    Setup?
}

model Stay {
  guildId String @id
  textId  String
  voiceId String
  Guild   Guild  @relation(fields: [guildId], references: [guildId])
}

model Dj {
  guildId String  @id
  mode    Boolean
  Guild   Guild   @relation(fields: [guildId], references: [guildId])
}

model Role {
  guildId String
  roleId  String
  Guild   Guild  @relation(fields: [guildId], references: [guildId])

  @@unique([guildId, roleId])
}

model Playlist {
  id     String  @id @default(uuid())
  userId String
  name   String
  tracks String? // Store the array of encoded tracks as a JSON string

  @@unique([userId, name])
}

model Setup {
  guildId   String @id
  textId    String
  messageId String
  Guild     Guild  @relation(fields: [guildId], references: [guildId])
}
