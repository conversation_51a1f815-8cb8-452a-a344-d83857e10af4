{"name": "lavamusic", "version": "5.0.0-beta", "description": "LavaMusic is a music bot for Discord, written in JavaScript using the Discord.js, Typescript, lavalink-client (Lavalink) library.", "main": "dist/index.js", "scripts": {"dev": "tsup --watch --onSuccess \"node dist/index.js\"", "start": "node dist/index.js", "db:push": "npx prisma db push", "db:migrate": "npx prisma migrate dev --name init", "build": "tsup", "lint": "eslint .", "lint:fix": "eslint --fix .", "format": "prettier --write \"**/*.{js,ts,tsx,md}\""}, "repository": {"type": "git", "url": "git+https://github.com/appujet/lavamusic.git"}, "keywords": ["discord", "music", "bot", "lavalink", "lavalink-client", "lavamusic", "typescript", "prisma"], "author": "appujet", "license": "GPL-3.0", "bugs": {"url": "https://github.com/appujet/lavamusic/issues"}, "homepage": "https://github.com/appujet/lavamusic#readme", "devDependencies": {"@appujet/eslint-config": "^0.0.3", "@types/i18n": "^0.13.12", "@types/node": "^22.14.1", "@types/signale": "^1.4.7", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.2", "prettier": "^3.5.3", "prisma": "^6.6.0", "tsup": "^8.4.0", "typescript": "^5.8.3"}, "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/helmet": "^13.0.1", "@fastify/jwt": "^9.1.0", "@fastify/sensible": "^6.0.3", "@prisma/client": "^6.6.0", "@swc/core": "^1.11.21", "@top-gg/sdk": "^3.1.6", "discord.js": "^14.18.0", "dotenv": "^16.5.0", "fastify": "^5.3.2", "fastify-plugin": "^5.0.1", "genius-lyrics-api": "^3.2.1", "i18n": "^0.15.1", "lavalink-client": "^2.5.0", "node-system-stats": "^1.3.0", "reflect-metadata": "^0.2.2", "signale": "^1.4.0", "socket.io": "^4.8.1", "topgg-autoposter": "^2.0.2", "tslib": "^2.8.1", "tsyringe": "^4.10.0", "undici": "^7.8.0", "zod": "^3.24.3"}, "signale": {"displayScope": true, "displayBadge": true, "displayDate": true, "displayFilename": true, "displayLabel": true, "displayTimestamp": true, "underlineLabel": true}}