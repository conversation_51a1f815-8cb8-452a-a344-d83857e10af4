run = "npm run start"
hidden = [".dist", ".config"]

[packager]
language = "nodejs"

[packager.features]
enabledForHosting = false
packageSearch = true
guessImports = true

[env]
XDG_CONFIG_HOME = "/home/<USER>/.config"

[nix]
channel = "stable-22_11"

[gitHubImport]
requiredFiles = [".replit", "replit.nix", ".config"]

[languages]

[languages.typescript]
pattern = "**/{*.ts,*.js,*.tsx,*.jsx}"

[languages.typescript.languageServer]
start = "typescript-language-server --stdio"
