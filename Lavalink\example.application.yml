server: # REST and WS server
  port: 2333 # The port that the server listens on
  address: 0.0.0.0
  http2:
    enabled: false # Whether to enable HTTP/2 support
plugins:
  jiosaavn:
    apiURL: "https://jiosaavn-plugin-api.vercel.app/api" # JioSaavn API URL
    playlistTrackLimit: 50 # The maximum number of tracks to return from given playlist (default 50 tracks)
    recommendationsTrackLimit: 10 # The maximum number of track to return from recommendations (default 10 tracks)
  dunctebot:
    ttsLanguage: 'en-US' # language of the TTS engine
    sources:
      # true = source enabled, false = source disabled
      getyarn: true # www.getyarn.io
      clypit: true # www.clyp.it
      tts: true # speak:Words to speak
      pornhub: true # should be self-explanatory
      reddit: true # should be self-explanatory
      ocremix: true # www.ocremix.org
      tiktok: true # tiktok.com
      mixcloud: true # mixcloud.com
      soundgasm: true # soundgasm.net
      pixeldrain: true # pixeldrain.com
  youtube:
    enabled: true
    oauth:
      enabled: false
    allowSearch: true
    allowDirectVideoIds: true
    allowDirectPlaylistIds: true
    clients:
      - TV
      - TVHTML5EMBEDDED
      - WEB
      - MWEB
      - WEBEMBEDDED
      - MUSIC
      - ANDROID_VR
      - ANDROID_MUSIC
      - IOS
    #oauth:
     # enabled: true # IF YOU RUN YOUR LAVALINK CHECK YOUR CONSOLE AND CLICK THE GOOGLE.COM/DEVICES LINK AND THERE IS A CODE THAT YOU NEED TO PUT IN TO THE GOOGLE AND USE A BURNER ACCOUNT OR ALT ACCOUNT THERE IS A POSSIBLE CHANGE YOU CAN GET BANNED FROM GOOGLE OR YOUTUBE SO JUST USE A BURNER ACCOUNT IN CASE.
    #pot:
   #   token: "" # THIS REQUIRE INSTALLING https://github.com/iv-org/youtube-trusted-session-generator THIS IS A PYTHON FILE INSTALL THE REQUIREMENTS AND RUN PYTON INDEX FILE AND AFTER RUNNING YOU WILL RECIVE AND TOKEN AND VISTOR DATA AND PASTE IT BELOW.
   #   visitorData: ""
    clientOptions:
      TV: #OAuth2 Client
        playback: true #Requires sign-in (OAuth2)
        playlistLoading: false
        searching: false
        videoLoading: false
      TVHTML5EMBEDDED: #OAuth2 Client
        playback: true #Requires sign-in (OAuth2)
        playlistLoading: false
        searching: true
        videoLoading: true
      WEB:
        playback: true
        playlistLoading: true
        searching: true
        videoLoading: true
      MWEB:
        playback: true
        playlistLoading: true
        searching: true
        videoLoading: true
      WEBEMBEDDED:
        playback: true
        playlistLoading: false
        searching: false
        videoLoading: true
      MUSIC:
        playback: false
        playlistLoading: false
        searching: true
        videoLoading: false
      ANDROID_VR:
        playback: true
        playlistLoading: true
        searching: true
        videoLoading: true
      ANDROID_MUSIC:
        playback: true
        playlistLoading: false
        searching: true
        videoLoading: true
      IOS:
        playback: true
        playlistLoading: true
        searching: true
        videoLoading: true
  lavasrc:
    providers: # Custom providers for track loading. This is the default
#      - "dzisrc:%ISRC%" # Deezer ISRC provider
#      - "dzsearch:%QUERY%" # Deezer search provider
      - "ytsearch:\"%ISRC%\"" # Will be ignored if track does not have an ISRC. See https://en.wikipedia.org/wiki/International_Standard_Recording_Code
      - "ytsearch:%QUERY%" # Will be used if track has no ISRC or no track could be found for the ISRC
#        you can add multiple other fallback sources here
    sources:
      spotify: false # Enable Spotify source
      applemusic: false # Enable Apple Music source
      deezer: false # Enable Deezer source
      yandexmusic: false # Enable Yandex Music source
      flowerytts: false # Enable Flowery TTS source
      youtube: true # Enable YouTube search source (https://github.com/topi314/LavaSearch)
      vkmusic: false # Enable Vk Music source
    lyrics-sources:
      spotify: false # Enable Spotify lyrics source
      deezer: false # Enable Deezer lyrics source
      youtube: true # Enable YouTube lyrics source
      yandexmusic: false # Enable Yandex Music lyrics source
      vkmusic: false # Enable Vk Music lyrics source
    spotify:
      clientId: "your client id"
      clientSecret: "your client secret"
      # spDc: "your sp dc cookie" # the sp dc cookie used for accessing the spotify lyrics api
      countryCode: "US" # the country code you want to use for filtering the artists top tracks. See https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
      playlistLoadLimit: 6 # The number of pages at 100 tracks each
      albumLoadLimit: 6 # The number of pages at 50 tracks each
      resolveArtistsInSearch: true # Whether to resolve artists in track search results (can be slow)
      localFiles: false # Enable local files support with Spotify playlists. Please note `uri` & `isrc` will be `null` & `identifier` will be `"local"`
    applemusic:
      countryCode: "US" # the country code you want to use for filtering the artists top tracks and language. See https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
      mediaAPIToken: "your apple music api token" # apple music api token
      playlistLoadLimit: 6 # The number of pages at 300 tracks each
      albumLoadLimit: 6 # The number of pages at 300 tracks each
    deezer:
      masterDecryptionKey: "your master decryption key" # the master key used for decrypting the deezer tracks. (yes this is not here you need to get it from somewhere else)
      arl: "your deezer arl" # the arl cookie used for accessing the deezer api this does not appear to be optional anymore
      formats: [ "FLAC", "MP3_320", "MP3_256", "MP3_128", "MP3_64", "AAC_64" ] # the formats you want to use for the deezer tracks. "FLAC", "MP3_320", "MP3_256" & "AAC_64" are only available for premium users and require a valid arl
    yandexmusic:
      accessToken: "your access token" # the token used for accessing the yandex music api. See https://github.com/topi314/LavaSrc#yandex-music
      playlistLoadLimit: 1 # The number of pages at 100 tracks each
      albumLoadLimit: 1 # The number of pages at 50 tracks each
      artistLoadLimit: 1 # The number of pages at 10 tracks each
    flowerytts:
      voice: "default voice" # (case-sensitive) get default voice from here https://api.flowery.pw/v1/tts/voices
      translate: false # whether to translate the text to the native language of voice
      silence: 0 # the silence parameter is in milliseconds. Range is 0 to 10000. The default is 0.
      speed: 1.0 # the speed parameter is a float between 0.5 and 10. The default is 1.0. (0.5 is half speed, 2.0 is double speed, etc.)
      audioFormat: "mp3" # supported formats are: mp3, ogg_opus, ogg_vorbis, aac, wav, and flac. Default format is mp3
    youtube:
      countryCode: "US" # the country code you want to use for searching lyrics via ISRC. See https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
    vkmusic:
      userToken: "your user token" # This token is needed for authorization in the api. Guide: https://github.com/topi314/LavaSrc#vk-music
      playlistLoadLimit: 1 # The number of pages at 50 tracks each
      artistLoadLimit: 1 # The number of pages at 10 tracks each
      recommendationsLoadLimit: 10 # Number of tracks
lavalink:
  plugins:
    - dependency: "com.github.appujet:jiosaavn-plugin:1.0.3"
      repository: "https://jitpack.io"
    - dependency: "com.dunctebot:skybot-lavalink-plugin:1.7.0"
      snapshot: false # set to true if you want to use snapshot builds
    - dependency: "com.github.topi314.lavasearch:lavasearch-plugin:1.0.0"
      snapshot: false # set to true if you want to use snapshot builds
    - dependency: "com.github.topi314.lavasrc:lavasrc-plugin:4.4.2"
      snapshot: false # set to true if you want to use snapshot builds
    - dependency: "com.github.topi314.sponsorblock:sponsorblock-plugin:3.0.1"
      snapshot: false # set to true if you want to use snapshot builds
    - dependency: "dev.lavalink.youtube:youtube-plugin:1.12.0"
      snapshot: false # set to true if you want to use snapshot builds
  pluginsDir: './plugins'
  server:
    password: "youshallnotpass"
    sources:
      # The default Youtube source is now deprecated and won't receive further updates. Please use https://github.com/lavalink-devs/youtube-source#plugin instead.
      youtube: false
      bandcamp: true
      soundcloud: true
      twitch: true
      vimeo: true
      mixer: true
      nico: true
      http: true # warning: keeping HTTP enabled without a proxy configured could expose your server's IP address.
      local: false
    filters: # All filters are enabled by default
      volume: true
      equalizer: true
      karaoke: true
      timescale: true
      tremolo: true
      vibrato: true
      distortion: true
      rotation: true
      channelMix: true
      lowPass: true
    bufferDurationMs: 400 # The duration of the NAS buffer. Higher values fare better against longer GC pauses. Duration <= 0 to disable JDA-NAS. Minimum of 40ms, lower values may introduce pauses.
    frameBufferDurationMs: 5000 # How many milliseconds of audio to keep buffered
    opusEncodingQuality: 10 # Opus encoder quality. Valid values range from 0 to 10, where 10 is best quality but is the most expensive on the CPU.
    resamplingQuality: MEDIUM # Quality of resampling operations. Valid values are LOW, MEDIUM and HIGH, where HIGH uses the most CPU.
    trackStuckThresholdMs: 10000 # The threshold for how long a track can be stuck. A track is stuck if does not return any audio data.
    youtubePlaylistLoadLimit: 6 # Number of pages at 100 each
    playerUpdateInterval: 5 # How frequently to send player updates to clients, in seconds
    nonAllocatingFrameBuffer: false # Setting to true reduces the number of allocations made by each player at the expense of frame rebuilding (e.g. non-instantaneous volume changes)
    useSeekGhosting: true # Seek ghosting is the effect where whilst a seek is in progress, the audio buffer is read from until empty, or until seek is ready.
    youtubeSearchEnabled: true
    soundcloudSearchEnabled: true
    gc-warnings: true
    #ratelimit:
      #ipBlocks: ["*******/8", "..."] # list of ip blocks
      #excludedIps: ["...", "..."] # ips which should be explicit excluded from usage by lavalink
      #strategy: "RotateOnBan" # RotateOnBan | LoadBalance | NanoSwitch | RotatingNanoSwitch
      #searchTriggersFail: true # Whether a search 429 should trigger marking the ip as failing
      #retryLimit: -1 # -1 = use default lavaplayer value | 0 = infinity | >0 = retry will happen this numbers times
    #youtubeConfig: # Required for avoiding all age restrictions by YouTube, some restricted videos still can be played without.
      #email: "your account mail" # Email of Google account
      #password: "your account password" # Password of Google account
    #httpConfig: # Useful for blocking bad-actors from ip-grabbing your music node and attacking it, this way only the http proxy will be attacked
      #proxyHost: "localhost" # Hostname of the proxy, (ip or domain)
      #proxyPort: 3128 # Proxy port, 3128 is the default for squidProxy
      #proxyUser: "" # Optional user for basic authentication fields, leave blank if you don't use basic auth
      #proxyPassword: "" # Password for basic authentication

metrics:
  prometheus:
    enabled: false
    endpoint: /metrics

sentry:
  dsn: ""
  environment: ""
#  tags:
#    some_key: some_value
#    another_key: another_value

logging:
  file:
    path: ./logs/

  level:
    # Set this to DEBUG to enable more detailed logging. Please note that this will likely spam your console.
    root: INFO
    # Set this to DEBUG to enable more detailed logging from Lavalink.
    lavalink: INFO
    # Youtube Oauth2 Handle logs
    dev.lavalink.youtube.http.YoutubeOauth2Handler: INFO

  request:
    enabled: true
    includeClientInfo: true
    includeHeaders: false
    includeQueryString: true
    includePayload: true
    maxPayloadLength: 10000

  logback:
    rollingpolicy:
      max-file-size: 1GB
      max-history: 30
